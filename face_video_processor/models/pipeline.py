import os.path as osp
import torch
import numpy as np

from .retinaface import Retina<PERSON>aceDetection
from .utils import LoadImage

class RetinaFaceDetectionPipeline:

    def __init__(self, model, **kwargs):
        """
        use `model` to create a face detection pipeline for prediction
        Args:
            model: path to local model directory.
        """
        # Initialize pipeline attributes (from base Pipeline class)
        self.model_dir = model
        self.device = kwargs.get('device', 'cpu')
        self._model_prepare = False
        ckpt_path = osp.join(model, 'pytorch_model.pt')
        print(f'loading model from {ckpt_path}')
        self.detector = RetinaFaceDetection(
            model_path=ckpt_path, device=self.device)
        print('load model done')

    def preprocess(self, input):
        img = LoadImage.convert_to_ndarray(input)
        img = img.astype(np.float32)
        result = {'img': img}
        return result

    def forward(self, input):
        result = self.detector(input)
        assert result is not None
        bboxes = result[0][:, :4].tolist()
        scores = result[0][:, 4].tolist()
        lms = result[1].tolist()
        return {
            'scores': scores,
            'boxes': bboxes,
            'keypoints': lms,
        }

    def postprocess(self, inputs):
        return inputs

    def __call__(self, input, *args, **kwargs):
        """Process input through the pipeline"""
        return self._process_single(input, *args, **kwargs)

    def _process_single(self, input, *args, **kwargs):
        """Process a single input"""
        preprocess_params = kwargs.get('preprocess_params', {})
        forward_params = kwargs.get('forward_params', {})
        postprocess_params = kwargs.get('postprocess_params', {})

        # Preprocess
        out = self.preprocess(input, **preprocess_params)

        # Forward pass
        with torch.no_grad():
            out = self.forward(out, **forward_params)

        # Postprocess
        out = self.postprocess(out, **postprocess_params)

        return out