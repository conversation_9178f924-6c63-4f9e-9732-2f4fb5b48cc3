"""
人脸检测器 - 核心人脸检测功能
"""

import threading
import time
import queue
import numpy as np
from typing import Dict, List, Any, Optional

from ..config.settings import (
    MIN_FACE_CONFIDENCE, MIN_FACE_SIZE, PARALLEL_WORKERS,
    CONFIDENCE_WEIGHT, BRIGHTNESS_WEIGHT, CONTRAST_WEIGHT,
    IDEAL_BRIGHTNESS_CENTER, CONTRAST_NORMALIZATION_FACTOR
)
from ..models.pipeline import RetinaFaceDetectionPipeline


class EnhancedFaceDetector:
    """增强的人脸检测器 - GPU优化版本"""

    def __init__(self, device: str = "auto"):
        """
        初始化人脸检测器

        Args:
            device: 设备类型 ("cuda", "cpu", "auto")
        """
        print("初始化GPU优化人脸检测器...")

        # 设备选择
        if device == "auto":
            import torch
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device

        print(f"使用设备: {self.device}")

        # 初始化检测器
        # 获取模型路径（相对于当前模块）
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        model_path = os.path.join(current_dir, "..", "models", "cv_resnet50_face-detection_retinaface")
        model_path = os.path.normpath(model_path)

        self.detector = RetinaFaceDetectionPipeline(model=model_path, device=self.device)

        # GPU批处理配置
        self.batch_size = PARALLEL_WORKERS if self.device == "cuda" else 1
        self.inference_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.inference_thread = None
        self.stop_inference = False

        print(f"✓ 人脸检测器初始化完成 (批大小: {self.batch_size})")

    def start_batch_inference_worker(self):
        """启动批处理推理工作线程"""
        if self.device == "cuda" and self.inference_thread is None:
            self.stop_inference = False
            self.inference_thread = threading.Thread(
                target=self._batch_inference_worker, daemon=True
            )
            self.inference_thread.start()
            print("✓ GPU批处理推理线程已启动")

    def stop_batch_inference_worker(self):
        """停止批处理推理工作线程"""
        if self.inference_thread is not None:
            self.stop_inference = True
            # 发送停止信号
            self.inference_queue.put(None)
            self.inference_thread.join(timeout=5)
            self.inference_thread = None
            print("✓ GPU批处理推理线程已停止")

    def _batch_inference_worker(self):
        """GPU批处理推理工作线程"""
        batch_items = []

        while not self.stop_inference:
            # 收集批处理项目
            while len(batch_items) < self.batch_size:
                item = self.inference_queue.get(timeout=0.1)
                if item is None:  # 停止信号
                    break
                batch_items.append(item)

            if not batch_items:
                continue

            # 批量推理
            if len(batch_items) == 1:
                # 单个推理
                item = batch_items[0]
                result = self._inference_from_memory(item['image_array'])
                self.result_queue.put({
                    'request_id': item['request_id'],
                    'result': result
                })
            else:
                # 批量推理
                self._process_batch(batch_items)

            batch_items.clear()

    def _process_batch(self, batch_items: List[Dict]):
        """处理一个批次的推理请求"""
        for item in batch_items:
            result = self._inference_from_memory(item['image_array'])
            self.result_queue.put({
                'request_id': item['request_id'],
                'result': result
            })

    def _inference_from_memory(self, image_array: np.ndarray) -> Dict[str, Any]:
        """从内存中的图像数组进行推理"""
        result = self.detect_faces_from_array(image_array)
        return result

    def detect_faces_from_array(self, image_array: np.ndarray) -> Dict[str, Any]:
        """从numpy数组检测人脸"""
        if self.device == "cpu" or self.inference_thread is None:
            # CPU模式或未启动批处理，直接推理
            return self._direct_inference(image_array)
        else:
            # GPU批处理模式
            return self._batch_inference_request(image_array)

    def _direct_inference(self, image_array: np.ndarray) -> Dict[str, Any]:
        """直接推理模式"""
        result = self.detector(image_array)
        return result

    def _batch_inference_request(self, image_array: np.ndarray) -> Dict[str, Any]:
        """批处理推理请求"""
        # 生成唯一请求ID
        request_id = f"{threading.get_ident()}_{int(time.time() * 1000000)}"

        # 提交推理请求
        self.inference_queue.put({
            'request_id': request_id,
            'image_array': image_array.copy()  # 复制数组以避免并发修改
        })

        # 等待结果
        while True:
            result_item = self.result_queue.get(timeout=10)  # 10秒超时
            if result_item['request_id'] == request_id:
                return result_item['result']
            else:
                # 不是我们的结果，放回队列
                self.result_queue.put(result_item)
                time.sleep(0.001)  # 短暂等待

    def parse_result(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析检测结果"""
        faces = []
        scores = result.get('scores', [])
        boxes = result.get('boxes', [])
        keypoints = result.get('keypoints', [])

        for i in range(len(scores)):
            if scores[i] < MIN_FACE_CONFIDENCE:
                continue
            face_info = {
                'confidence': scores[i],
                'bbox': boxes[i],
                'landmarks': keypoints[i] if i < len(keypoints) else None
            }
            faces.append(face_info)

        return faces

    def calculate_quality_score(self, faces: List[Dict], brightness: float, contrast: float) -> float:
        """计算帧质量评分"""
        # 人脸置信度评分
        max_confidence = max([face['confidence'] for face in faces])
        confidence_score = max_confidence * CONFIDENCE_WEIGHT

        # 亮度评分 - 理想亮度在设定范围内
        brightness_score = 1.0 - abs(IDEAL_BRIGHTNESS_CENTER - brightness) / IDEAL_BRIGHTNESS_CENTER
        brightness_score = max(0, brightness_score) * BRIGHTNESS_WEIGHT

        # 对比度评分
        contrast_score = min(contrast / CONTRAST_NORMALIZATION_FACTOR, 1.0) * CONTRAST_WEIGHT

        return confidence_score + brightness_score + contrast_score
