#!/usr/bin/env python3
"""
配置文件
包含服务器的所有配置常量和设置
"""

import logging

# 版本信息
VERSION = "1.0.0"
SERVICE_NAME = "视频分析服务器"

# 缓存配置
CACHE_FILE = "timestamp_cache.json"
CACHE_VERSION = "1.0"

# 支持的视频格式
VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
)

# YOLO模型配置
YOLO_MODEL_REPO = "AdamCodd/YOLOv11n-face-detection"
YOLO_MODEL_FILENAME = "model.pt"

# 视频分析参数
SAMPLE_INTERVAL_SEC = 2  # 采样间隔（秒）
COLLECTION_DURATION_SEC = 5  # 收集帧的持续时间（秒）
MIN_BRIGHTNESS = 10  # 最小亮度阈值
MIN_CONTRAST = 5  # 最小对比度阈值
MIN_DETECTION_SIZE = 30  # 最小检测框尺寸
MIN_DETECTION_RATIO = 0.1  # 检测框最小占画面比例
MIN_CONFIDENCE = 0.75  # 最小置信度阈值

# 服务器默认配置
DEFAULT_HOST = '0.0.0.0'
DEFAULT_PORT = 5000

# 日志配置
LOG_LEVEL = logging.INFO
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_FILE = None  # 自动生成日志文件名
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
LOG_BACKUP_COUNT = 5
LOG_ENABLE_CONSOLE = True
LOG_ENABLE_FILE = True

# 面部方向精细化配置
FACE_ORIENTATION_REFINE = True  # 默认启用面部方向精细化

# Hopenet模型配置 (相对于webp_generator目录)
import os
_current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
HOPENET_MODEL_PATH = os.path.join(_current_dir, "deep-head-pose", "hopenet_robust_alpha1.pkl")
HOPENET_PERPENDICULARITY_THRESHOLD = 45.0  # 面部垂直度阈值（度）
HOPENET_BBOX_EXPANSION = 50  # 人脸边界框扩展像素数

# API端点信息
API_ENDPOINTS = {
    "/analyze": "POST - 分析视频并生成 WebP 动画 (支持 resolution 参数)",
    "/analyze_frame": "POST - 分析视频并生成单帧 WebP 图片 (支持 resolution 参数)",
    "/health": "GET - 健康检查",
    "/cache": "GET - 获取缓存信息, DELETE - 清空缓存",
    "/status": "GET - 系统状态信息 (包含面部方向精细化状态)",
    "/logs": "GET - 日志配置信息",
    "/logs/level": "POST - 动态设置日志级别",
    "/": "GET - API 信息"
}
