#!/usr/bin/env python3
"""
验证相对路径转换 - 检查项目中是否还有硬编码的绝对路径
"""

import os
import re
import sys
from pathlib import Path

def find_absolute_paths_in_file(file_path):
    """在文件中查找绝对路径"""
    absolute_paths = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        # 查找可能的绝对路径模式
        patterns = [
            r'/home/<USER>"\s\']+',  # /home/<USER>/... 路径
            r'/mnt/[^"\s\']+',   # /mnt/... 路径
            r'/media/[^"\s\']+', # /media/... 路径
            r'/tmp/[^"\s\']+',   # /tmp/... 路径（某些情况下可能需要保留）
            r'/usr/[^"\s\']+',   # /usr/... 路径
            r'/opt/[^"\s\']+',   # /opt/... 路径
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                # 过滤掉一些可能的误报
                if not any(exclude in match for exclude in [
                    '/tmp/video_repair',  # 这个可能是用户指定的临时目录
                    '/tmp/my_repair',     # 示例中的临时目录
                    'LOG_FILE',           # 日志文件配置
                    'comment',            # 注释中的路径
                    '#',                  # 注释行
                ]):
                    absolute_paths.append(match)
                    
    except Exception as e:
        print(f"⚠️ 读取文件失败 {file_path}: {e}")
        
    return absolute_paths

def scan_directory(directory, extensions=None):
    """扫描目录中的文件"""
    if extensions is None:
        extensions = ['.py', '.md', '.txt', '.json', '.yml', '.yaml']
    
    results = {}
    
    for root, dirs, files in os.walk(directory):
        # 跳过一些不需要检查的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in [
            '__pycache__', 'node_modules', '.git', '.vscode'
        ]]
        
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                absolute_paths = find_absolute_paths_in_file(file_path)
                
                if absolute_paths:
                    results[file_path] = absolute_paths
                    
    return results

def main():
    """主函数"""
    print("🔍 扫描项目中的绝对路径...")
    print("=" * 60)
    
    # 扫描当前目录
    current_dir = os.getcwd()
    results = scan_directory(current_dir)
    
    if not results:
        print("✅ 未发现硬编码的绝对路径！")
        print("\n🎉 所有路径都已正确转换为相对路径")
        return 0
    
    print("⚠️ 发现以下文件包含绝对路径:")
    print()
    
    total_files = len(results)
    total_paths = sum(len(paths) for paths in results.values())
    
    for file_path, absolute_paths in results.items():
        rel_path = os.path.relpath(file_path, current_dir)
        print(f"📁 {rel_path}")
        
        for path in absolute_paths:
            print(f"   ❌ {path}")
        print()
    
    print("=" * 60)
    print(f"📊 统计: {total_files} 个文件包含 {total_paths} 个绝对路径")
    
    # 提供修复建议
    print("\n💡 修复建议:")
    print("1. 使用 os.path.expanduser('~') 替换 /home/<USER>")
    print("2. 使用 os.path.join() 和相对路径构建路径")
    print("3. 使用 os.path.dirname(__file__) 获取当前文件目录")
    print("4. 使用 os.path.normpath() 规范化路径")
    
    return 1

def test_relative_path_functions():
    """测试相对路径函数是否正常工作"""
    print("\n🧪 测试相对路径函数...")
    
    # 测试用户目录扩展
    user_path = os.path.expanduser('~/.cache/test')
    print(f"✅ 用户目录扩展: {user_path}")
    
    # 测试相对路径构建
    current_file = os.path.abspath(__file__)
    current_dir = os.path.dirname(current_file)
    relative_path = os.path.join(current_dir, '..', 'face_video_processor')
    normalized_path = os.path.normpath(relative_path)
    print(f"✅ 相对路径构建: {normalized_path}")
    
    # 测试路径存在性
    if os.path.exists(normalized_path):
        print(f"✅ 路径存在验证: {normalized_path}")
    else:
        print(f"⚠️ 路径不存在: {normalized_path}")

if __name__ == "__main__":
    exit_code = main()
    test_relative_path_functions()
    sys.exit(exit_code)
