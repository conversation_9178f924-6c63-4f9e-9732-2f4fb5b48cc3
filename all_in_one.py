import os
import argparse
import logging
from tqdm import tqdm
# from modelscope.pipelines import pipeline
# from modelscope.utils.constant import Tasks
import json
import time
from datetime import datetime, timedelta
from tqdm.contrib.concurrent import thread_map
# from concurrent.futures import ThreadPoolExecutor
# from tqdm.contrib.concurrent import thread_map, process_map
from pgl_sum.video_summarization_pipeline import VideoSummarizationPipeline, TooShortVideoError, build_ffmpeg_cmd, print_gpu_memory
import subprocess
from batch_seg_concat import process_video_clips

timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M")

cwd = os.getcwd()

# Supported video extensions
VIDEO_EXTENSIONS = (
    '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
    '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
)
VIDEO_EXTENSIONS_UPPER = tuple(ext.upper() for ext in VIDEO_EXTENSIONS)
VIDEO_EXTENSIONS = VIDEO_EXTENSIONS + VIDEO_EXTENSIONS_UPPER

def pause_during_night(start_hour=13, end_hour=1):
    now = datetime.now()
    # 判断是否处于 21:00–次日09:00
    if now.hour >= start_hour or now.hour < end_hour:
        # 计算下一个唤醒时间
        if now.hour >= start_hour:
            wake = datetime(now.year, now.month, now.day, end_hour) + timedelta(days=1)
        else:
            wake = datetime(now.year, now.month, now.day, end_hour)
        delay = (wake - now).total_seconds()
        print(f"当前时间 {now.time()} 在禁用时段，程序将暂停 {delay/3600:.2f} 小时")
        time.sleep(delay)  # 阻塞当前线程至区间结束 :contentReference[oaicite:6]{index=6}
    else:
        print(f"当前时间 {now.time()} 在允许时段，继续执行")


def process_videos(input_base: str):
    # 使用相对于用户目录的路径
    import os
    model_path = os.path.expanduser('~/.cache/modelscope/hub/models/iic/cv_googlenet_pgl-video-summarization')
    summarization_pipeline = VideoSummarizationPipeline(model=model_path)
    # Collect all video files first
    video_paths = []
    for root, _, files in os.walk(input_base):
        if "thumbnails" in root:
            continue
        for fname in files:
            if fname.endswith(VIDEO_EXTENSIONS):
                video_paths.append(os.path.join(root, fname))

    # Ensure base output directory exists
    log_dir = os.path.join(cwd, 'log')
    os.makedirs(log_dir, exist_ok=True)


    # log_file = os.path.join(log_dir,'log.txt')
    # log = open(log_file, "w")
    # os.dup2(log.fileno(), 1)
    # os.dup2(log.fileno(), 2)

    # Path for record of processed files
    analyzed_video_log = os.path.join(cwd, 'analyzed_video.log')
    analyzed_error_log = os.path.join(cwd, 'analyzed_error.log')
    analyzed_converted_log = os.path.join(cwd, 'analyzed_converted.log')

    # Load already processed files
    analyzed_set = set()
    if os.path.exists(analyzed_video_log):
       with open(analyzed_video_log, 'r') as pf:
           analyzed_set = set(line.strip() for line in pf if line.strip())

    analyzed_error_set = set()
    if os.path.exists(analyzed_error_log):
       with open(analyzed_error_log, 'r') as pf:
           analyzed_error_set = set(line.strip() for line in pf if line.strip())

    analyzed_converted_set = set()
    if os.path.exists(analyzed_converted_log):
        with open(analyzed_converted_log, 'r') as pf:
            analyzed_converted_set = set(line.strip() for line in pf if line.strip())

    analyzed_set = set.union(analyzed_set, analyzed_error_set, analyzed_converted_set)

    # Filter out already processed videos
    analyze_pending_paths = [p for p in video_paths if p not in analyzed_set]

    # Setup separate loggers for success and error
    logfile = os.path.join(log_dir, timestamp + '.log')

    # Success logger
    logger = logging.getLogger('all_in_one')
    logger.setLevel(logging.INFO)
    handler = logging.FileHandler(logfile)
    handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)

    for input_path in tqdm(analyze_pending_paths, desc="Analyzing", unit="file"):
    # def run_inference(input_path):
        # pause_during_night()
        # Determine output directory based on relative path and filename
        # if True:
        # print(f"now processing: {input_path}")

        root = os.path.dirname(input_path).strip("/")
        fname = os.path.basename(input_path)
        json_output_dir = os.path.join(cwd, root)
        os.makedirs(json_output_dir, exist_ok=True)
        base_name, _ = os.path.splitext(fname)
        output_json = os.path.join(json_output_dir, base_name+".json")

        try:
            result = summarization_pipeline(input_path)
            with open(output_json, "w") as f:
                json.dump(result,f)
            logger.info(f"Analyze Success: {input_path}")
            with open(analyzed_video_log, 'a') as pf:
                pf.write(f"{input_path}\n")
        except TooShortVideoError:
            logger.info(f"Video Too Short: {input_path}")
            with open(analyzed_error_log, 'a') as pf:
                pf.write(f"{input_path}\n")
            continue
        except Exception as e:
            logger.error(
                f"Analyze Failed: '{input_path}'\n {e}. Try convert."
            )
            with open(analyzed_error_log, 'a') as pf:
                pf.write(f"{input_path}\n")
            cvt_path, cmd = build_ffmpeg_cmd(input_path)
            try:
                ret = subprocess.run(cmd[0], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                assert ret.returncode == 0, ret.stderr
                result = summarization_pipeline(cvt_path)
                with open(output_json, "w") as f:
                    json.dump(result,f)
                logger.info(f"Analyze Success: {cvt_path}")
                with open(analyzed_converted_log, 'a') as pf:
                    pf.write(f"{input_path}\n")
            except Exception as e:
                logger.error(
                    f"Analyze Failed Again after Convert: '{cvt_path}'.\n {e}\n"
                )
                subprocess.run(cmd[1], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)


    json_paths = []
    for root, _, files in os.walk(os.path.join(cwd, "mnt")):
        if "thumbnails" in root:
            continue
        for fname in files:
            if fname.lower().endswith(".json"):
                json_paths.append(os.path.join(root, fname))

    concated_video_log = os.path.join(cwd, 'concated_video.log')
    concated_error_log = os.path.join(cwd, 'concated_error.log')
    concated_converted_log = os.path.join(cwd, 'concated_converted.log')

    concated_video_set = set()
    if os.path.exists(concated_video_log):
        with open(concated_video_log, 'r') as pf:
            concated_video_set = set(line.strip() for line in pf if line.strip())

    concated_error_set = set()
    if os.path.exists(concated_error_log):
       with open(concated_error_log, 'r') as pf:
           concated_error_set = set(line.strip() for line in pf if line.strip())

    concat_converted_set = set()
    if os.path.exists(concated_converted_log):
        with open(concated_converted_log, 'r') as pf:
            concat_converted_set = set(line.strip() for line in pf if line.strip())

    concated_video_set = set.union(concated_video_set, concated_error_set, concat_converted_set)

    concate_pending_jsons = [p for p in json_paths if p not in concated_video_set]
    def run_concat(input_json):
    # for input_path in tqdm(pending_video_paths, desc="Processing videos", unit="file"):
        with open(input_json, "r") as f:
            result = json.load(f)

        video_path = result.get("video_name", None)
        if video_path is None:
            purpose_video_path = "/"+"/".join(input_json.split("/")[4:])
            purpose_video_basename = os.path.splitext(purpose_video_path)[0]
            for ext in VIDEO_EXTENSIONS:
                video_path = purpose_video_basename + ext
                if os.path.exists(video_path): break
            else:
                logger.error(f"Source video not found: '{input_json}'\n")
                with open(concated_error_log, 'a') as pf:
                    pf.write(f"{input_json}\n")
                return

        video_root = os.path.dirname(video_path)

        thumbnail_dir = os.path.join(video_root, "thumbnails")
        os.makedirs(thumbnail_dir, exist_ok=True)

        fname = os.path.basename(video_path)
        output_video = os.path.join(thumbnail_dir, fname)

        try:
            process_video_clips(result.get('output', []), video_path, output_video)
            logger.info(f"Concat Success: {input_json}")
            with open(concated_video_log, 'a') as pf:
                pf.write(f"{input_json}\n")
        except ValueError as e1:
            with open(concated_error_log, 'a') as pf:
                pf.write(f"{input_json}\n")
            logger.error(
                f"Concat Failed: '{input_json}'\n{e1}\n"
            )
        except Exception as e2:
            with open(concated_error_log, 'a') as pf:
                pf.write(f"{input_json}\n")
            try:
                if "fix" in video_path:
                    logger.error(
                        f"Concat Failed: '{input_json}'\n{e2}\nConvert video already existed. Skipping conversion."
                    )
                    with open(concated_error_log, 'a') as pf:
                        pf.write(f"{input_json}\n")
                    return

                cvt_path, cmd = build_ffmpeg_cmd(video_path)
                ret = subprocess.run(cmd[0], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                assert ret.returncode == 0, ret.stderr

                fname = os.path.basename(cvt_path)
                output_video = os.path.join(thumbnail_dir, fname)
                process_video_clips(result.get('output', []), cvt_path, output_video)

                # Log success and mark as processe
                logger.info(f"Concat Success: {cvt_path}")
                with open(analyzed_converted_log, 'a') as pf:
                    pf.write(f"{input_json}\n")

            except Exception as e3:
                logger.error(
                    f"Concat Failed Again after Convert: '{input_json}'\n{e3}\n"
                )
                subprocess.run(cmd[1], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

    thread_map(run_concat, concate_pending_jsons, max_workers=32, desc="Concating Videos")


    # analyze_pending_paths = analyze_pending_paths[:4]
    # results = thread_map(run_inference, analyze_pending_paths, max_workers=1, desc="Threads")

    # for record in results:
    #     out = record["output"]
    #     print(f"Output={out}")

def main():
    parser = argparse.ArgumentParser(
        description='Batch process videos with whisperx across a directory tree.'
    )
    parser.add_argument(
        'input_base',
        help='Path to the base input directory containing video files.'
    )

    args = parser.parse_args()

    process_videos(args.input_base)


if __name__ == '__main__':
    main()
