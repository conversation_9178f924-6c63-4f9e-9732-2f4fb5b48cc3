#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的视频分析器 - 包含级联修复策略和错误处理机制
Enhanced Video Analyzer with Cascading Repair Strategies and Error Handling

作者: AI Assistant
日期: 2024-06-04
"""

import os
import sys
import json
import time
import shutil
import signal
import logging
import argparse
import subprocess
import threading
import gc
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import psutil

# GPU监控相关导入
try:
    import torch
    CUDA_AVAILABLE = torch.cuda.is_available()
except ImportError:
    CUDA_AVAILABLE = False
    torch = None

try:
    import GPUtil
except ImportError:
    GPUtil = None

# 导入原有的视频处理模块
from pgl_sum.video_summarization_pipeline import VideoSummarizationPipeline, TooShortVideoError


class ErrorType(Enum):
    """视频错误类型枚举"""
    INVALID_NAL_UNIT = "invalid_nal_unit"
    MISSING_MOOV_ATOM = "missing_moov_atom"
    CORRUPTED_HEADER = "corrupted_header"
    AUDIO_CORRUPTION = "audio_corruption"
    CODEC_ERROR = "codec_error"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"


@dataclass
class RepairResult:
    """修复结果数据类"""
    success: bool
    method_used: str
    output_path: Optional[str] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0


@dataclass
class VideoInfo:
    """视频信息数据类"""
    path: str
    size_mb: float
    duration_seconds: Optional[float] = None
    codec: Optional[str] = None
    resolution: Optional[Tuple[int, int]] = None
    fps: Optional[float] = None
    error_type: Optional[ErrorType] = None



class DiskSpaceManager:
    """磁盘空间管理器"""

    def __init__(self, min_free_gb: float = 5.0):
        self.min_free_gb = min_free_gb

    def get_free_space_gb(self, path: str) -> float:
        """获取指定路径的可用磁盘空间（GB）"""
        statvfs = os.statvfs(path)
        free_bytes = statvfs.f_frsize * statvfs.f_bavail
        return free_bytes / (1024 ** 3)

    def has_enough_space(self, path: str, required_gb: float = None) -> bool:
        """检查是否有足够的磁盘空间"""
        free_gb = self.get_free_space_gb(path)
        required = required_gb or self.min_free_gb
        return free_gb > required

    def cleanup_temp_files(self, temp_dir: str, max_age_hours: int = 24):
        """清理临时文件"""
        if not os.path.exists(temp_dir):
            return

        current_time = time.time()
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_age_hours = (current_time - os.path.getmtime(file_path)) / 3600
                    if file_age_hours > max_age_hours:
                        os.remove(file_path)
                        logging.info(f"清理临时文件: {file_path}")
                except Exception as e:
                    logging.warning(f"清理文件失败 {file_path}: {e}")


class TimeoutManager:
    """超时管理器"""

    def __init__(self):
        self.timeout_occurred = False
        self.timer = None

    def start_timeout(self, timeout_seconds: int, callback=None):
        """启动超时计时器"""
        def timeout_handler():
            self.timeout_occurred = True
            if callback:
                callback()

        self.timer = threading.Timer(timeout_seconds, timeout_handler)
        self.timer.start()

    def cancel_timeout(self):
        """取消超时计时器"""
        if self.timer:
            self.timer.cancel()
            self.timer = None

    def is_timeout(self) -> bool:
        """检查是否超时"""
        return self.timeout_occurred


class VideoErrorAnalyzer:
    """视频错误分析器"""

    def __init__(self):
        self.error_patterns = {
            ErrorType.INVALID_NAL_UNIT: [
                "Invalid NAL unit size",
                "Error splitting the input into NAL units",
                "Invalid NAL unit 0, skipping"
            ],
            ErrorType.MISSING_MOOV_ATOM: [
                "moov atom not found",
                "could not find codec parameters",
                "Invalid data found when processing input",
                "Error opening input"
            ],
            ErrorType.CORRUPTED_HEADER: [
                "Invalid data found when processing input",
                "Header missing"
            ],
            ErrorType.AUDIO_CORRUPTION: [
                "channel element",
                "Reserved bit set",
                "Number of bands",
                "exceeds limit"
            ],
            ErrorType.CODEC_ERROR: [
                "Unsupported codec",
                "codec not currently supported"
            ]
        }

    def analyze_error(self, error_message: str) -> ErrorType:
        """分析错误消息，确定错误类型"""
        error_message_lower = error_message.lower()

        for error_type, patterns in self.error_patterns.items():
            for pattern in patterns:
                if pattern.lower() in error_message_lower:
                    return error_type

        return ErrorType.UNKNOWN_ERROR

    def get_video_info(self, video_path: str) -> VideoInfo:
        """获取视频文件信息"""
        try:
            # 获取文件大小
            size_mb = os.path.getsize(video_path) / (1024 * 1024)

            # 使用 ffprobe 获取视频信息
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                data = json.loads(result.stdout)

                # 提取视频流信息
                video_stream = None
                for stream in data.get('streams', []):
                    if stream.get('codec_type') == 'video':
                        video_stream = stream
                        break

                duration = None
                codec = None
                resolution = None
                fps = None

                if video_stream:
                    duration = float(video_stream.get('duration', 0))
                    codec = video_stream.get('codec_name')
                    width = video_stream.get('width')
                    height = video_stream.get('height')
                    if width and height:
                        resolution = (int(width), int(height))

                    # 计算帧率
                    r_frame_rate = video_stream.get('r_frame_rate', '0/1')
                    if '/' in r_frame_rate:
                        num, den = map(int, r_frame_rate.split('/'))
                        if den != 0:
                            fps = num / den

                return VideoInfo(
                    path=video_path,
                    size_mb=size_mb,
                    duration_seconds=duration,
                    codec=codec,
                    resolution=resolution,
                    fps=fps
                )
            else:
                # ffprobe 失败，分析错误类型
                error_type = self.analyze_error(result.stderr)
                return VideoInfo(
                    path=video_path,
                    size_mb=size_mb,
                    error_type=error_type
                )

        except Exception as e:
            logging.error(f"获取视频信息失败 {video_path}: {e}")
            return VideoInfo(
                path=video_path,
                size_mb=0,
                error_type=ErrorType.UNKNOWN_ERROR
            )


class VideoRepairStrategies:
    """视频修复策略类"""

    def __init__(self, temp_dir: str = "/tmp/video_repair"):
        self.temp_dir = temp_dir
        os.makedirs(temp_dir, exist_ok=True)

    def _get_temp_path(self, original_path: str, suffix: str) -> str:
        """生成临时文件路径"""
        base_name = os.path.splitext(os.path.basename(original_path))[0]
        return os.path.join(self.temp_dir, f"{base_name}_{suffix}.mp4")

    def strategy_basic_reencoding(self, video_path: str, timeout_seconds: int = 3600) -> RepairResult:
        """策略1: 基础重编码 - 使用标准参数重新编码"""
        start_time = time.time()
        output_path = self._get_temp_path(video_path, "basic_reencoded")

        try:
            cmd = [
                "ffmpeg", "-y",
                "-probesize", "100M", "-analyzeduration", "100M",
                "-fflags", "+genpts+discardcorrupt",
                "-err_detect", "ignore_err",
                "-threads", "16",
                "-i", video_path,
                "-c:v", "libx264", "-preset", "medium", "-crf", "23",
                "-c:a", "aac", "-b:a", "128k",
                "-movflags", "+faststart",
                "-avoid_negative_ts", "make_zero",
                output_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout_seconds
            )

            processing_time = time.time() - start_time

            if result.returncode == 0 and os.path.exists(output_path):
                return RepairResult(
                    success=True,
                    method_used="basic_reencoding",
                    output_path=output_path,
                    processing_time=processing_time
                )
            else:
                return RepairResult(
                    success=False,
                    method_used="basic_reencoding",
                    error_message=result.stderr,
                    processing_time=processing_time
                )

        except subprocess.TimeoutExpired:
            return RepairResult(
                success=False,
                method_used="basic_reencoding",
                error_message="处理超时",
                processing_time=time.time() - start_time
            )
        except Exception as e:
            return RepairResult(
                success=False,
                method_used="basic_reencoding",
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def strategy_aggressive_error_concealment(self, video_path: str, timeout_seconds: int = 3600) -> RepairResult:
        """策略2: 激进的错误隐藏 - 跳过损坏的帧"""
        start_time = time.time()
        output_path = self._get_temp_path(video_path, "error_concealed")

        try:
            cmd = [
                "ffmpeg", "-y",
                "-fflags", "+genpts+discardcorrupt+igndts",
                "-err_detect", "ignore_err",
                "-skip_frame", "nokey",  # 跳过非关键帧
                "-threads", "16",
                "-i", video_path,
                "-c:v", "libx264", "-preset", "ultrafast", "-crf", "28",
                "-c:a", "aac", "-b:a", "96k",
                "-movflags", "+faststart",
                "-avoid_negative_ts", "make_zero",
                "-max_muxing_queue_size", "1024",
                output_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout_seconds
            )

            processing_time = time.time() - start_time

            if result.returncode == 0 and os.path.exists(output_path):
                return RepairResult(
                    success=True,
                    method_used="aggressive_error_concealment",
                    output_path=output_path,
                    processing_time=processing_time
                )
            else:
                return RepairResult(
                    success=False,
                    method_used="aggressive_error_concealment",
                    error_message=result.stderr,
                    processing_time=processing_time
                )

        except subprocess.TimeoutExpired:
            return RepairResult(
                success=False,
                method_used="aggressive_error_concealment",
                error_message="处理超时",
                processing_time=time.time() - start_time
            )
        except Exception as e:
            return RepairResult(
                success=False,
                method_used="aggressive_error_concealment",
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def strategy_video_only_extraction(self, video_path: str, timeout_seconds: int = 3600) -> RepairResult:
        """策略3: 仅提取视频流 - 丢弃音频"""
        start_time = time.time()
        output_path = self._get_temp_path(video_path, "video_only")

        try:
            cmd = [
                "ffmpeg", "-y",
                "-fflags", "+genpts+discardcorrupt",
                "-err_detect", "ignore_err",
                "-threads", "16",
                "-i", video_path,
                "-an",  # 丢弃音频
                "-c:v", "libx264", "-preset", "fast", "-crf", "25",
                "-movflags", "+faststart",
                "-avoid_negative_ts", "make_zero",
                output_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout_seconds
            )

            processing_time = time.time() - start_time

            if result.returncode == 0 and os.path.exists(output_path):
                return RepairResult(
                    success=True,
                    method_used="video_only_extraction",
                    output_path=output_path,
                    processing_time=processing_time
                )
            else:
                return RepairResult(
                    success=False,
                    method_used="video_only_extraction",
                    error_message=result.stderr,
                    processing_time=processing_time
                )

        except subprocess.TimeoutExpired:
            return RepairResult(
                success=False,
                method_used="video_only_extraction",
                error_message="处理超时",
                processing_time=time.time() - start_time
            )
        except Exception as e:
            return RepairResult(
                success=False,
                method_used="video_only_extraction",
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def strategy_raw_copy_with_repair(self, video_path: str, timeout_seconds: int = 3600) -> RepairResult:
        """策略4: 原始拷贝修复 - 尝试修复容器格式"""
        start_time = time.time()
        output_path = self._get_temp_path(video_path, "raw_repaired")

        try:
            cmd = [
                "ffmpeg", "-y",
                "-fflags", "+genpts",
                "-err_detect", "ignore_err",
                "-i", video_path,
                "-c", "copy",  # 直接拷贝流
                "-avoid_negative_ts", "make_zero",
                "-map", "0",
                output_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout_seconds
            )

            processing_time = time.time() - start_time

            if result.returncode == 0 and os.path.exists(output_path):
                return RepairResult(
                    success=True,
                    method_used="raw_copy_with_repair",
                    output_path=output_path,
                    processing_time=processing_time
                )
            else:
                return RepairResult(
                    success=False,
                    method_used="raw_copy_with_repair",
                    error_message=result.stderr,
                    processing_time=processing_time
                )

        except subprocess.TimeoutExpired:
            return RepairResult(
                success=False,
                method_used="raw_copy_with_repair",
                error_message="处理超时",
                processing_time=time.time() - start_time
            )
        except Exception as e:
            return RepairResult(
                success=False,
                method_used="raw_copy_with_repair",
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def strategy_segment_based_repair(self, video_path: str, timeout_seconds: int = 3600) -> RepairResult:
        """策略5: 基于分段的修复 - 分段处理并合并"""
        start_time = time.time()
        output_path = self._get_temp_path(video_path, "segment_repaired")

        try:
            # 首先尝试获取视频时长
            probe_cmd = [
                'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
                '-of', 'csv=p=0', video_path
            ]

            probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=30)

            if probe_result.returncode != 0:
                return RepairResult(
                    success=False,
                    method_used="segment_based_repair",
                    error_message="无法获取视频时长",
                    processing_time=time.time() - start_time
                )

            try:
                duration = float(probe_result.stdout.strip())
            except ValueError:
                duration = 3600  # 默认1小时

            # 分段处理（每段10分钟）
            segment_duration = 600
            segments = []

            for start_time_seg in range(0, int(duration), segment_duration):
                segment_path = self._get_temp_path(video_path, f"segment_{start_time_seg}")

                cmd = [
                    "ffmpeg", "-y",
                    "-ss", str(start_time_seg),
                    "-t", str(segment_duration),
                    "-fflags", "+genpts+discardcorrupt",
                    "-err_detect", "ignore_err",
                    "-i", video_path,
                    "-c:v", "libx264", "-preset", "fast", "-crf", "25",
                    "-c:a", "aac", "-b:a", "128k",
                    "-avoid_negative_ts", "make_zero",
                    segment_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)

                if result.returncode == 0 and os.path.exists(segment_path):
                    segments.append(segment_path)

            if not segments:
                return RepairResult(
                    success=False,
                    method_used="segment_based_repair",
                    error_message="没有成功的分段",
                    processing_time=time.time() - start_time
                )

            # 合并分段
            concat_file = os.path.join(self.temp_dir, "concat_list.txt")
            with open(concat_file, 'w') as f:
                for segment in segments:
                    f.write(f"file '{segment}'\n")

            concat_cmd = [
                "ffmpeg", "-y",
                "-f", "concat",
                "-safe", "0",
                "-i", concat_file,
                "-c", "copy",
                output_path
            ]

            concat_result = subprocess.run(concat_cmd, capture_output=True, text=True, timeout=300)

            # 清理临时文件
            for segment in segments:
                try:
                    os.remove(segment)
                except:
                    pass
            try:
                os.remove(concat_file)
            except:
                pass

            processing_time = time.time() - start_time

            if concat_result.returncode == 0 and os.path.exists(output_path):
                return RepairResult(
                    success=True,
                    method_used="segment_based_repair",
                    output_path=output_path,
                    processing_time=processing_time
                )
            else:
                return RepairResult(
                    success=False,
                    method_used="segment_based_repair",
                    error_message=concat_result.stderr,
                    processing_time=processing_time
                )

        except subprocess.TimeoutExpired:
            return RepairResult(
                success=False,
                method_used="segment_based_repair",
                error_message="处理超时",
                processing_time=time.time() - start_time
            )
        except Exception as e:
            return RepairResult(
                success=False,
                method_used="segment_based_repair",
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def strategy_untrunc_repair(self, video_path: str, timeout_seconds: int = 3600) -> RepairResult:
        """策略6: 使用untrunc修复 - 专门修复moov atom缺失问题"""
        start_time = time.time()
        output_path = self._get_temp_path(video_path, "untrunc_repaired")

        try:
            # 首先需要找一个参考文件（同目录下的正常mp4文件）
            video_dir = os.path.dirname(video_path)
            reference_file = None

            # 查找同目录下的其他mp4文件作为参考
            for file in os.listdir(video_dir):
                if file.endswith(('.mp4', '.mov', '.m4v')) and file != os.path.basename(video_path):
                    ref_path = os.path.join(video_dir, file)
                    if os.path.getsize(ref_path) > 1024 * 1024:  # 至少1MB
                        # 快速检查这个文件是否正常
                        test_cmd = ["ffprobe", "-v", "quiet", ref_path]
                        test_result = subprocess.run(test_cmd, capture_output=True, timeout=10)
                        if test_result.returncode == 0:
                            reference_file = ref_path
                            break

            if not reference_file:
                # 如果没有找到参考文件，尝试创建一个简单的参考文件
                reference_file = self._get_temp_path(video_path, "reference")
                ref_cmd = [
                    "ffmpeg", "-y", "-f", "lavfi", "-i", "testsrc=duration=1:size=320x240:rate=1",
                    "-c:v", "libx264", "-t", "1", reference_file
                ]
                ref_result = subprocess.run(ref_cmd, capture_output=True, text=True, timeout=30)
                if ref_result.returncode != 0:
                    return RepairResult(
                        success=False,
                        method_used="untrunc_repair",
                        error_message="无法创建参考文件",
                        processing_time=time.time() - start_time
                    )

            # 使用untrunc修复
            untrunc_path = os.path.join(os.getcwd(), "untrunc")
            if not os.path.exists(untrunc_path):
                untrunc_path = "untrunc"  # 假设在PATH中

            cmd = [untrunc_path, "-dst", output_path, reference_file, video_path]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout_seconds
            )

            processing_time = time.time() - start_time

            if result.returncode == 0 and os.path.exists(output_path):
                return RepairResult(
                    success=True,
                    method_used="untrunc_repair",
                    output_path=output_path,
                    processing_time=processing_time
                )
            else:
                return RepairResult(
                    success=False,
                    method_used="untrunc_repair",
                    error_message=result.stderr or result.stdout,
                    processing_time=processing_time
                )

        except subprocess.TimeoutExpired:
            return RepairResult(
                success=False,
                method_used="untrunc_repair",
                error_message="处理超时",
                processing_time=time.time() - start_time
            )
        except Exception as e:
            return RepairResult(
                success=False,
                method_used="untrunc_repair",
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def strategy_hex_repair(self, video_path: str, timeout_seconds: int = 3600) -> RepairResult:
        """策略7: 十六进制修复 - 尝试修复文件头"""
        start_time = time.time()
        output_path = self._get_temp_path(video_path, "hex_repaired")

        try:
            # 读取文件的前几KB
            with open(video_path, 'rb') as f:
                header = f.read(8192)

            # 检查是否是MP4文件但缺少正确的头部
            if b'ftyp' not in header[:100]:
                # 尝试添加标准MP4头部
                mp4_header = b'\x00\x00\x00\x20ftypmp42\x00\x00\x00\x00mp42isom'

                with open(output_path, 'wb') as out_f:
                    out_f.write(mp4_header)
                    with open(video_path, 'rb') as in_f:
                        # 跳过可能损坏的头部
                        in_f.seek(0)
                        data = in_f.read()
                        out_f.write(data)

                # 测试修复后的文件
                test_cmd = ["ffprobe", "-v", "quiet", output_path]
                test_result = subprocess.run(test_cmd, capture_output=True, timeout=30)

                processing_time = time.time() - start_time

                if test_result.returncode == 0:
                    return RepairResult(
                        success=True,
                        method_used="hex_repair",
                        output_path=output_path,
                        processing_time=processing_time
                    )

            return RepairResult(
                success=False,
                method_used="hex_repair",
                error_message="文件头部看起来正常，无需修复",
                processing_time=time.time() - start_time
            )

        except Exception as e:
            return RepairResult(
                success=False,
                method_used="hex_repair",
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def strategy_partial_recovery(self, video_path: str, timeout_seconds: int = 3600) -> RepairResult:
        """策略8: 部分恢复 - 尝试恢复文件的可用部分"""
        start_time = time.time()
        output_path = self._get_temp_path(video_path, "partial_recovered")

        try:
            # 使用dd命令尝试恢复文件的前面部分
            file_size = os.path.getsize(video_path)

            # 尝试恢复前80%的数据
            recover_size = int(file_size * 0.8)

            dd_cmd = [
                "dd", f"if={video_path}", f"of={output_path}",
                f"bs=1M", f"count={recover_size // (1024*1024)}",
                "conv=noerror,sync"
            ]

            dd_result = subprocess.run(dd_cmd, capture_output=True, text=True, timeout=300)

            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                # 尝试用ffmpeg修复这个部分文件
                temp_fixed = self._get_temp_path(video_path, "partial_fixed")

                fix_cmd = [
                    "ffmpeg", "-y",
                    "-fflags", "+genpts+discardcorrupt+igndts",
                    "-err_detect", "ignore_err",
                    "-i", output_path,
                    "-c", "copy",
                    "-avoid_negative_ts", "make_zero",
                    temp_fixed
                ]

                fix_result = subprocess.run(fix_cmd, capture_output=True, text=True, timeout=timeout_seconds)

                processing_time = time.time() - start_time

                if fix_result.returncode == 0 and os.path.exists(temp_fixed):
                    # 清理中间文件
                    try:
                        os.remove(output_path)
                    except:
                        pass

                    return RepairResult(
                        success=True,
                        method_used="partial_recovery",
                        output_path=temp_fixed,
                        processing_time=processing_time
                    )

            return RepairResult(
                success=False,
                method_used="partial_recovery",
                error_message="部分恢复失败",
                processing_time=time.time() - start_time
            )

        except Exception as e:
            return RepairResult(
                success=False,
                method_used="partial_recovery",
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def strategy_force_container_repair(self, video_path: str, timeout_seconds: int = 3600) -> RepairResult:
        """策略9: 强制容器修复 - 使用更激进的ffmpeg参数"""
        start_time = time.time()
        output_path = self._get_temp_path(video_path, "force_repaired")

        try:
            # 使用最激进的ffmpeg参数
            cmd = [
                "ffmpeg", "-y",
                "-analyzeduration", "2147483647",
                "-probesize", "2147483647",
                "-fflags", "+genpts+discardcorrupt+igndts+ignidx",
                "-err_detect", "ignore_err",
                "-f", "mp4",
                "-i", video_path,
                "-c:v", "copy",
                "-c:a", "copy",
                "-avoid_negative_ts", "make_zero",
                "-fflags", "+fastseek",
                "-movflags", "+faststart+frag_keyframe+empty_moov",
                output_path
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout_seconds
            )

            processing_time = time.time() - start_time

            if result.returncode == 0 and os.path.exists(output_path):
                return RepairResult(
                    success=True,
                    method_used="force_container_repair",
                    output_path=output_path,
                    processing_time=processing_time
                )
            else:
                return RepairResult(
                    success=False,
                    method_used="force_container_repair",
                    error_message=result.stderr,
                    processing_time=processing_time
                )

        except subprocess.TimeoutExpired:
            return RepairResult(
                success=False,
                method_used="force_container_repair",
                error_message="处理超时",
                processing_time=time.time() - start_time
            )
        except Exception as e:
            return RepairResult(
                success=False,
                method_used="force_container_repair",
                error_message=str(e),
                processing_time=time.time() - start_time
            )


class EnhancedVideoAnalyzer:
    """增强的视频分析器主类"""

    def __init__(self, model_path: str, temp_dir: str = "/tmp/video_repair"):
        self.model_path = model_path
        self.temp_dir = temp_dir
        self.summarization_pipeline = None

        # 初始化各个组件
        self.error_analyzer = VideoErrorAnalyzer()
        self.repair_strategies = VideoRepairStrategies(temp_dir)
        self.disk_manager = DiskSpaceManager()

        # 支持的视频扩展名
        self.video_extensions = (
            '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm',
            '.mpeg', '.mpg', '.m4v', '.3gp', '.ts'
        )

        # 设置日志
        self.setup_logging()

        # 修复策略优先级（按成功率和速度排序）
        self.repair_strategies_order = [
            'strategy_raw_copy_with_repair',
            'strategy_untrunc_repair',
            'strategy_hex_repair',
            'strategy_force_container_repair',
            'strategy_basic_reencoding',
            'strategy_video_only_extraction',
            'strategy_aggressive_error_concealment',
            'strategy_partial_recovery',
            'strategy_segment_based_repair'
        ]

    def setup_logging(self):
        """设置日志系统"""
        timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M")
        log_file = f"log/enhanced_analyzer_{timestamp}.log"

        os.makedirs("log", exist_ok=True)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )

        self.logger = logging.getLogger(__name__)

    def initialize_pipeline(self):
        """初始化视频摘要管道"""
        if self.summarization_pipeline is None:
            try:
                self.summarization_pipeline = VideoSummarizationPipeline(self.model_path)
                self.logger.info("视频摘要管道初始化成功")
            except Exception as e:
                self.logger.error(f"视频摘要管道初始化失败: {e}")
                raise

    def calculate_timeout(self, video_info: VideoInfo) -> int:
        """根据视频信息计算合理的超时时间"""
        base_timeout = 300  # 5分钟基础超时

        # 根据文件大小调整
        size_factor = min(video_info.size_mb / 100, 10)  # 每100MB增加1倍，最多10倍

        # 根据时长调整
        duration_factor = 1
        if video_info.duration_seconds:
            duration_factor = min(video_info.duration_seconds / 3600, 5)  # 每小时增加1倍，最多5倍

        timeout = int(base_timeout * (1 + size_factor + duration_factor))
        return min(timeout, 7200)  # 最大2小时

    def get_prioritized_strategies(self, error_type: ErrorType) -> List[str]:
        """根据错误类型获取优先级排序的修复策略"""
        if error_type == ErrorType.MISSING_MOOV_ATOM:
            # 对于moov atom缺失，优先使用untrunc和hex修复
            return [
                'strategy_untrunc_repair',
                'strategy_hex_repair',
                'strategy_force_container_repair',
                'strategy_raw_copy_with_repair',
                'strategy_partial_recovery',
                'strategy_basic_reencoding',
                'strategy_video_only_extraction',
                'strategy_aggressive_error_concealment',
                'strategy_segment_based_repair'
            ]
        elif error_type == ErrorType.INVALID_NAL_UNIT:
            # 对于NAL单元错误，优先使用错误隐藏
            return [
                'strategy_aggressive_error_concealment',
                'strategy_basic_reencoding',
                'strategy_video_only_extraction',
                'strategy_raw_copy_with_repair',
                'strategy_segment_based_repair',
                'strategy_partial_recovery',
                'strategy_untrunc_repair',
                'strategy_hex_repair'
            ]
        elif error_type == ErrorType.AUDIO_CORRUPTION:
            # 对于音频损坏，优先提取视频流
            return [
                'strategy_video_only_extraction',
                'strategy_basic_reencoding',
                'strategy_raw_copy_with_repair',
                'strategy_aggressive_error_concealment',
                'strategy_segment_based_repair',
                'strategy_partial_recovery',
                'strategy_untrunc_repair',
                'strategy_hex_repair'
            ]
        else:
            # 默认策略顺序
            return self.repair_strategies_order

    def repair_video_with_cascading_strategies(self, video_path: str, video_info: VideoInfo) -> RepairResult:
        """使用级联策略修复视频"""
        self.logger.info(f"开始修复视频: {video_path}")
        self.logger.info(f"视频信息: 大小={video_info.size_mb:.1f}MB, 错误类型={video_info.error_type}")

        # 检查磁盘空间
        required_space = video_info.size_mb * 3 / 1024  # 需要3倍文件大小的空间（GB）
        if not self.disk_manager.has_enough_space(self.temp_dir, required_space):
            self.logger.warning(f"磁盘空间不足，清理临时文件...")
            self.disk_manager.cleanup_temp_files(self.temp_dir, max_age_hours=1)

            if not self.disk_manager.has_enough_space(self.temp_dir, required_space):
                return RepairResult(
                    success=False,
                    method_used="disk_space_check",
                    error_message="磁盘空间不足"
                )

        # 计算超时时间
        timeout_seconds = self.calculate_timeout(video_info)
        self.logger.info(f"设置超时时间: {timeout_seconds}秒")

        # 根据错误类型获取优先级策略
        strategies = self.get_prioritized_strategies(video_info.error_type or ErrorType.UNKNOWN_ERROR)

        # 按优先级尝试修复策略
        for strategy_name in strategies:
            self.logger.info(f"尝试修复策略: {strategy_name}")

            try:
                strategy_method = getattr(self.repair_strategies, strategy_name)
                result = strategy_method(video_path, timeout_seconds)

                if result.success:
                    self.logger.info(f"修复成功! 使用策略: {strategy_name}, 耗时: {result.processing_time:.1f}秒")
                    return result
                else:
                    self.logger.warning(f"策略 {strategy_name} 失败: {result.error_message}")

            except Exception as e:
                self.logger.error(f"策略 {strategy_name} 执行异常: {e}")
                continue

        # 所有策略都失败
        return RepairResult(
            success=False,
            method_used="all_strategies_failed",
            error_message="所有修复策略都失败了"
        )

    def analyze_video(self, video_path: str) -> Dict[str, Any]:
        """分析单个视频文件"""
        self.logger.info(f"开始分析视频: {video_path}")

        # 初始化管道
        if self.summarization_pipeline is None:
            self.initialize_pipeline()

        # 获取视频信息
        video_info = self.error_analyzer.get_video_info(video_path)

        try:
            # 首先尝试直接分析
            self.logger.info(f"🎬 开始视频摘要分析...")
            result = self.summarization_pipeline(video_path)

            self.logger.info(f"直接分析成功: {video_path}")
            return {
                'success': True,
                'method': 'direct_analysis',
                'result': result,
                'video_info': video_info
            }

        except TooShortVideoError:
            self.logger.info(f"视频太短，跳过: {video_path}")
            return {
                'success': False,
                'method': 'too_short',
                'error': 'Video too short',
                'video_info': video_info
            }

        except Exception as e:
            self.logger.warning(f"直接分析失败: {video_path}, 错误: {e}")

            # 分析错误类型 - 检查多个错误源
            error_messages = [str(e)]

            # 尝试用ffprobe获取更详细的错误信息
            try:
                probe_cmd = ['ffprobe', '-v', 'error', video_path]
                probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=30)
                if probe_result.stderr:
                    error_messages.append(probe_result.stderr)
            except:
                pass

            # 分析所有错误消息
            for error_msg in error_messages:
                error_type = self.error_analyzer.analyze_error(error_msg)
                if error_type != ErrorType.UNKNOWN_ERROR:
                    video_info.error_type = error_type
                    break
            else:
                video_info.error_type = ErrorType.UNKNOWN_ERROR

            # 尝试修复
            repair_result = self.repair_video_with_cascading_strategies(video_path, video_info)

            if repair_result.success:
                try:
                    # 使用修复后的文件进行分析
                    result = self.summarization_pipeline(repair_result.output_path)
                    self.logger.info(f"修复后分析成功: {video_path}")

                    # 清理临时文件
                    try:
                        os.remove(repair_result.output_path)
                    except:
                        pass

                    return {
                        'success': True,
                        'method': f'repaired_with_{repair_result.method_used}',
                        'result': result,
                        'video_info': video_info,
                        'repair_time': repair_result.processing_time
                    }

                except Exception as e2:
                    self.logger.error(f"修复后分析仍然失败: {video_path}, 错误: {e2}")

                    # 清理临时文件
                    try:
                        os.remove(repair_result.output_path)
                    except:
                        pass

                    return {
                        'success': False,
                        'method': f'repaired_but_failed_{repair_result.method_used}',
                        'error': str(e2),
                        'video_info': video_info,
                        'repair_time': repair_result.processing_time
                    }
            else:
                self.logger.error(f"视频修复失败: {video_path}, 错误: {repair_result.error_message}")
                return {
                    'success': False,
                    'method': 'repair_failed',
                    'error': repair_result.error_message,
                    'video_info': video_info
                }

    def get_failed_videos_to_process(self) -> List[str]:
        """获取需要重新处理的失败视频列表"""
        failed_videos = set()

        # 读取错误日志
        if os.path.exists('analyzed_error.log'):
            with open('analyzed_error.log', 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and os.path.exists(line):
                        failed_videos.add(line)

        # 排除已经成功重编码的视频
        if os.path.exists('analyzed_converted.log'):
            with open('analyzed_converted.log', 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line in failed_videos:
                        failed_videos.remove(line)

        return list(failed_videos)

    def process_failed_videos(self, max_videos: int = None):
        """批量处理失败的视频"""
        failed_videos = self.get_failed_videos_to_process()

        if not failed_videos:
            self.logger.info("没有需要处理的失败视频")
            return

        if max_videos:
            failed_videos = failed_videos[:max_videos]

        self.logger.info(f"开始处理 {len(failed_videos)} 个失败的视频")

        # 创建结果日志文件
        timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M")
        success_log = f"enhanced_success_{timestamp}.log"
        error_log = f"enhanced_error_{timestamp}.log"

        success_count = 0
        error_count = 0

        for i, video_path in enumerate(failed_videos, 1):
            self.logger.info(f"处理进度: {i}/{len(failed_videos)} - {video_path}")

            try:
                result = self.analyze_video(video_path)

                if result['success']:
                    success_count += 1

                    # 保存分析结果
                    output_dir = os.path.dirname(video_path)
                    base_name = os.path.splitext(os.path.basename(video_path))[0]
                    output_json = os.path.join(output_dir, f"{base_name}.json")

                    with open(output_json, 'w', encoding='utf-8') as f:
                        json.dump(result['result'], f, ensure_ascii=False, indent=2)

                    # 记录成功日志
                    with open(success_log, 'a', encoding='utf-8') as f:
                        f.write(f"{video_path}\t{result['method']}\n")

                    self.logger.info(f"✓ 成功处理: {video_path} (方法: {result['method']})")

                else:
                    error_count += 1

                    # 记录错误日志
                    with open(error_log, 'a', encoding='utf-8') as f:
                        f.write(f"{video_path}\t{result['method']}\t{result.get('error', 'Unknown error')}\n")

                    self.logger.error(f"✗ 处理失败: {video_path} (方法: {result['method']}, 错误: {result.get('error', 'Unknown')})")

            except Exception as e:
                error_count += 1
                self.logger.error(f"✗ 处理异常: {video_path}, 错误: {e}")

                with open(error_log, 'a', encoding='utf-8') as f:
                    f.write(f"{video_path}\texception\t{str(e)}\n")

        # 清理临时文件
        self.disk_manager.cleanup_temp_files(self.temp_dir, max_age_hours=0)

        self.logger.info(f"批量处理完成! 成功: {success_count}, 失败: {error_count}")
        self.logger.info(f"成功日志: {success_log}")
        self.logger.info(f"错误日志: {error_log}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='增强的视频分析器 - 包含级联修复策略',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 处理所有失败的视频
  python enhanced_video_analyzer.py

  # 只处理前10个失败的视频
  python enhanced_video_analyzer.py --max-videos 10

  # 指定临时目录
  python enhanced_video_analyzer.py --temp-dir /tmp/my_repair

  # 分析单个视频文件
  python enhanced_video_analyzer.py --single-video /path/to/video.mp4
        """
    )

    # 获取默认模型路径（相对于用户缓存目录）
    import os
    default_model_path = os.path.expanduser('~/.cache/modelscope/hub/models/iic/cv_googlenet_pgl-video-summarization')

    parser.add_argument(
        '--model-path',
        default=default_model_path,
        help='视频摘要模型路径'
    )

    parser.add_argument(
        '--temp-dir',
        default='/tmp/video_repair',
        help='临时文件目录'
    )

    parser.add_argument(
        '--max-videos',
        type=int,
        help='最大处理视频数量'
    )

    parser.add_argument(
        '--single-video',
        help='分析单个视频文件'
    )

    args = parser.parse_args()

    # 创建增强分析器
    analyzer = EnhancedVideoAnalyzer(
        model_path=args.model_path,
        temp_dir=args.temp_dir
    )

    try:
        if args.single_video:
            # 分析单个视频
            if not os.path.exists(args.single_video):
                print(f"错误: 视频文件不存在: {args.single_video}")
                sys.exit(1)

            result = analyzer.analyze_video(args.single_video)

            # 处理VideoInfo对象的序列化
            if 'video_info' in result and hasattr(result['video_info'], '__dict__'):
                result['video_info'] = {
                    'path': result['video_info'].path,
                    'size_mb': result['video_info'].size_mb,
                    'duration_seconds': result['video_info'].duration_seconds,
                    'codec': result['video_info'].codec,
                    'resolution': result['video_info'].resolution,
                    'fps': result['video_info'].fps,
                    'error_type': result['video_info'].error_type.value if result['video_info'].error_type else None
                }

            print(f"分析结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

        else:
            # 批量处理失败的视频
            analyzer.process_failed_videos(max_videos=args.max_videos)

    except KeyboardInterrupt:
        print("\n用户中断处理")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
